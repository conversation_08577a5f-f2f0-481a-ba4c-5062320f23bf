"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[542],{7542:function(t,e,n){n.r(e),n.d(e,{trackCTAClick:function(){return i},trackEvent:function(){return c},trackPackageClick:function(){return a},trackPageView:function(){return o}});let c=(t,e,n,c)=>{window.gtag&&window.gtag("event",t,{event_category:e,event_label:n,value:c})},a=(t,e)=>{c("click","package",t,parseInt(e.replace("€","")))},i=(t,e)=>{c("click","cta","".concat(t,"_").concat(e))},o=t=>{window.gtag&&window.gtag("config","GA_MEASUREMENT_ID",{page_title:t,page_location:window.location.href})}}}]);